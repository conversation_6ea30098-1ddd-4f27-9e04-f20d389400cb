<script setup lang="ts">
  import { computed, defineProps, defineEmits, ref, PropType, watch, onMounted, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { marked } from 'marked';
  // markdown.ts markdown-it 14.1.0

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    streamCall: {
      type: Boolean,
      default: false,
      required: false,
    },
    response: {
      type: Object as PropType<any>,
    },
    sessionId: {
      type: String,
      default: 'default',
    },
    completeAnswer: {
      type: Boolean,
      default: false,
    },
    isPC: {
      type: Boolean,
      default: true,
    },
  });

  const emits = defineEmits(['update:visible', 'update:completeAnswer', 'send', 'stop']);
  const chatContainer = ref<HTMLElement | null>(null);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emits('update:visible', value),
  });

  const completeAnswer = computed({
    get: () => props.completeAnswer,
    set: (value) => emits('update:completeAnswer', value),
  });

  const isReply = ref(false);
  const isLoading = ref(false);
  const msg = ref<string>('');

  const loadChatHistory = () => {
    const history = localStorage.getItem(`chat_history_${props.sessionId}`);
    return history ? JSON.parse(history) : [];
  };

  const chatCatch = ref<any[]>(loadChatHistory());

  const saveChatHistory = () => {
    localStorage.setItem(`chat_history_${props.sessionId}`, JSON.stringify(chatCatch.value));
  };

  // 初始化时加载历史记录
  onMounted(() => {
    chatCatch.value = loadChatHistory();
  });

  const handleSend = () => {
    if (!msg.value || msg.value === '') {
      Message.clear('top');
      Message.error('发送消息不能为空');
      return;
    }
    isReply.value = true;
    isLoading.value = true;
    chatCatch.value.push({ role: 'user', msg: msg.value });
    chatCatch.value.push({ role: 'robot', msg: '' });
    saveChatHistory();
    emits('send', msg.value);
  };

  const handleStop = () => {
    isReply.value = false;
    isLoading.value = false;
    emits('stop');
  };

  const handleCopy = (message: any) => {
    navigator.clipboard.writeText(message).then(() => {
      Message.clear('top');
      Message.success('已复制');
    });
  };

  const clearChatHistory = () => {
    chatCatch.value = [];
    localStorage.removeItem(`chat_history_${props.sessionId}`);
  };
  const handleScrollToBottom = async () => {
    await nextTick();

    if (chatContainer.value) {
      chatContainer.value.scrollTo({
        top: chatContainer.value.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  watch(
    () => props.response,
    async (newVal: any) => {
      /* if (newVal && completeAnswer.value) {
        chatCatch.value.push({ role: 'robot', msg: newVal });
        saveChatHistory();
      } else if (newVal) { */
      chatCatch.value[chatCatch.value.length - 1].msg = newVal;
      // }
      isLoading.value = false;
      isReply.value = false;
      msg.value = '';
      await handleScrollToBottom();
    },
    { deep: true, immediate: true },
  );

  // watch(
  //   () => props.response,
  //   async (newVal: any) => {
  //     if (newVal?.rawResponse) {
  //       chatCatch.value.push({ role: 'robot', msg: newVal.rawResponse });
  //       saveChatHistory();
  //     }
  //     isLoading.value = false;
  //     isReply.value = false;
  //     msg.value = '';
  //     await handleScrollToBottom();
  //   },
  //   { deep: true, immediate: true },
  // );

  // watch(
  //   () => props.sessionId,
  //   (newVal) => {
  //     localStorage.setItem(`behavior_chat_sessionId`, newVal);
  //     chatCatch.value = loadChatHistory();
  //   },
  // );
  defineExpose({
    saveChatHistory,
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    fullscreen
    :closable="false"
    :draggable="true"
    v-bind="$attrs"
    :body-style="{ padding: 0, margin: 0 }"
  >
    <template #title>
      <slot name="title">
        <div class="flex justify-between items-center w-full">
          <span class="font-bold">{{ '行为分析小诸葛' }}</span>
          <div class="flex justify-center space-x-2">
            <a-button
              size="mini"
              type="outline"
              class="clearBtn"
              shape="round"
              status="danger"
              @click="clearChatHistory"
            >
              <template #icon>
                <icon-delete />
              </template>
              清空记录
            </a-button>

            <a-button
              v-if="isPC"
              size="mini"
              type="outline"
              shape="round"
              class="closeBtn"
              @click="modalVisible = false"
            >
              <template #icon>
                <icon-close />
              </template>
              关闭对话
            </a-button>
          </div>
        </div>
      </slot>
    </template>
    <slot name="content">
      <a-spin :loading="isLoading" class="w-full h-full">
        <div ref="chatContainer" class="w-full h-full overflow-auto px-4 py-2 bg-white rounded space-y-4">
          <div
            v-for="(message, index) in chatCatch"
            :key="index"
            class="flex items-start overflow-scroll"
            :class="message.role === 'user' ? 'justify-end mr-[10%]' : 'justify-start ml-[10%]'"
          >
            <!-- 助手头像 -->
            <div
              v-if="message.role !== 'user' && isPC"
              :class="[
                'w-9 h-9 mr-2 rounded-full bg-[#e0f0ff] border border-[#c6e0ff]',
                'shadow flex items-center justify-center text-[#0468b5]',
              ]"
            >
              <icon-robot size="18" />
            </div>

            <!--dialog-->
            <div
              class="rounded-lg shadow text-sm relative group"
              :class="
                (message.role === 'user'
                  ? 'bg-sky-400 text-white rounded-br-none px-6 shadow-lg'
                  : 'bg-gray-50 text-gray-800 rounded-bl-none px-[80px] py-[40px]',
                isPC ? 'max-w-[70%]' : 'max-w-[100%]')
              "
            >
              <icon-copy
                v-if="message.role !== 'user'"
                :class="[
                  'absolute right-1 top-1 cursor-pointer opacity-0',
                  'group-hover:opacity-100 transition-opacity duration-200',
                ]"
                @click="handleCopy(message.msg)"
              />
              <icon-loading v-if="!message?.msg" />
              <div v-else class="markdown-content" v-html="marked(message?.msg || '')" />
            </div>

            <!-- 用户头像 -->
            <div
              v-if="message.role === 'user' && isPC"
              :class="[
                'w-9 h-9 ml-2 rounded-full bg-gradient-to-br from-sky-400 to-blue-600',
                'shadow-md text-white text-sm font-bold flex items-center justify-center',
              ]"
            >
              U
            </div>
          </div>
          <div class="absolute bottom-6 right-10">
            <slot name="moreAction">
              <div class="flex justify-center items-center space-x-2">
                <div
                  class="rounded-full cursor-pointer bg-gray-100 text-black flex flex-justify-center items-center p-2"
                  @click="handleScrollToBottom"
                >
                  <icon-down size="20" />
                </div>
              </div>
            </slot>
          </div>
        </div>
      </a-spin>
    </slot>

    <template #footer>
      <slot name="footer">
        <div class="bg-gray-50 rounded-[8px] border mx-[10%]">
          <a-textarea
            v-model="msg"
            class="max-h-[50px] inputBox"
            style="border-radius: 8px; border: none"
            placeholder="问一问ai"
            @keydown.enter="handleSend"
          />
          <div class="flex justify-end items-center mb-3 mr-2 space-x-2">
            <slot name="toolBar"></slot>
            <a-tooltip :content="isReply ? '等待回复' : '发送'">
              <a-button
                v-if="!isReply"
                shape="round"
                style="background-color: #d5ecff; color: #0468b5"
                @click="handleSend"
              >
                <template #icon>
                  <icon-send size="15" />
                </template>
              </a-button>
              <a-button v-else shape="round" style="background-color: #ffdddf; color: #ff121e" @click="handleStop">
                <template #icon>
                  <icon-record-stop />
                </template>
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </slot>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
  @tailwind base;
  @tailwind components;
  @tailwind utilities;

  .inputBox {
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
    resize: none;
    &:hover {
      background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
    }
  }

  .clearBtn {
    color: red;
    border: #ff7a81 1px solid;
    &:hover {
      color: white;
      border: #ff444e 1px solid;
      background-color: #ff535e;
      font-weight: 700;
    }
  }
  .closeBtn {
    color: black;
    border: gray 1px solid;
    &:hover {
      color: white;
      border: gray 1px solid;
      background-color: #333333;
      font-weight: 700;
    }
  }

  ::v-deep .markdown-content {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      font-weight: bold;
    }

    h1 {
      font-size: 2em;
    }
    h2 {
      font-size: 1.5em;
    }
    h3 {
      font-size: 1.17em;
    }
    h4 {
      font-size: 1em;
    }
    h5 {
      font-size: 0.83em;
    }
    h6 {
      font-size: 0.67em;
    }
    p {
      margin: 1em 0;
      line-height: 1.5;
    }
    ul,
    ol {
      margin: 1em 0;
      padding-left: 2em;
    }
    a {
      color: #0366d6;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    pre {
      background-color: #f6f8fa;
      border-radius: 3px;
      padding: 16px;
      overflow: auto;
    }
    code {
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 3px;
      padding: 0.2em 0.4em;
    }
    blockquote {
      border-left: 4px solid #dfe2e5;
      color: #6a737d;
      padding: 0 1em;
      margin: 0 0 1em 0;
    }
    table {
      border-collapse: collapse;
      margin: 1em 0;
      width: 100%;
      th,
      td {
        border: 1px solid #dfe2e5;
        padding: 6px 13px;
      }
      th {
        background-color: #f6f8fa;
      }
    }
  }
</style>
