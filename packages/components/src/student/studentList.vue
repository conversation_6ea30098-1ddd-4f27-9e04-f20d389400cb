<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { computed, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { getClientRole, PROJECT_URLS } from '@repo/env-config';
  import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
  import { CommonApi } from '@repo/infrastructure/crud';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import StudentArchive from './studentArchive.vue';
  import UpdateStatusModal from './components/updateStatusModal.vue';
  import SetGradeClassModal from './components/setGradeClassModal.vue';
  import StudentSendPlanModal from './components/studentSendPlanModal.vue';
  import SetGraduationModal from './components/setGraduationModal.vue';
  import CommitteeReferenceModal from './components/committeeReferenceModal.vue';

  const props = defineProps({
    modulePath: {
      type: String,
      required: true,
    },
    title: {
      type: String,
    },
    visibleColumns: {
      type: Array,
      default: () => [
        'name',
        'symbol',
        'nation',
        'fusionSchool',
        'hasSendPlan',
        'status',
        'gender',
        'age',
        'birthday',
        'disorders',
      ],
    },
    defaultQueryParams: {
      type: Object,
      default: () => ({ sort: '-id' }),
    },
    customSchema: {
      type: Object,
    },
    tableActionProps: {
      type: Object,
      default: () => ({}),
    },
  });

  const queryParams = computed(() => {
    return {
      ...props.defaultQueryParams,
    };
  });

  const tableRef = ref<any>(null);
  const schema = ref<any>();
  const archiveVisible = ref(false);
  const updateStatusVisible = ref(false);
  const setGradeClassVisible = ref(false);
  const sendEducationPlanVisible = ref(false);
  const graduationVisible = ref(false);
  const specialCommitteeVisible = ref(false);
  const currentStudent = ref<any>();
  const currentRowActionConfig = ref<any>({});

  const handleRowAction = (action: any, row: any, config: any) => {
    currentStudent.value = row;
    currentRowActionConfig.value = config || {};
    switch (action.key) {
      case 'archive':
        archiveVisible.value = true;
        break;
      case 'updateStatus':
        updateStatusVisible.value = true;
        break;
      case 'setGradeClass':
        setGradeClassVisible.value = true;
        break;
      case 'sendEducationPlan':
        sendEducationPlanVisible.value = true;
        break;
      case 'graduation':
        graduationVisible.value = true;
        break;
      case 'specialCommittee':
        specialCommitteeVisible.value = true;
        break;
      case 'cancelGraduation':
        Modal.confirm({
          title: '请再次确认',
          content: `确认取消 ${row.name} 的毕业状态吗？`,
          onOk: async () => {
            await request(`/resourceRoom/student/cancelGraduation/${row.id}`, {
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              method: 'PUT',
            });
            Message.success('取消毕业成功');
            await tableRef.value?.loadData();
          },
        });
        break;
      default:
        break;
    }
  };
  const myGradeClass = ref();
  const loadMyGradeClass = async () => {
    try {
      const { data: res } = await request(`/resourceRoom/gradeClass`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      myGradeClass.value = res.items.map((item) => item.id);
    } finally {
      /**/
    }
  };
  const clientRole = getClientRole();
  const menuStore = useUserMenuStore();
  const unitNature = menuStore.getCurrentUnitNature();

  const getStudentFromClusterTableRowClass = (record) => {
    if (unitNature === '特殊教育学校') {
      if (clientRole === 'Manager') return '';
      if (myGradeClass.value.includes(record.gradeClass?.id)) return '';
      return 'clusterStudent-row';
    }
    return '';
  };

  const handleRefresh = async () => {
    await tableRef.value?.loadData();
  };

  onMounted(async () => {
    await loadMyGradeClass();
    if (props.customSchema) {
      schema.value = props.customSchema;
    } else {
      schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/student');
    }
  });

  defineExpose({
    tableRef: () => {
      return tableRef.value;
    },
  });
</script>

<template>
  <a-card v-if="schema" size="small">
    <template #title>
      <table-action
        v-if="tableRef"
        class="flex-1"
        :module-path="modulePath"
        :schema="schema"
        :table="tableRef"
        component-size="mini"
        v-bind="tableActionProps"
        @row-action="handleRowAction"
      >
        <template #title>
          <slot name="title">{{ title }}学生信息管理</slot>
        </template>
        <template #extra-actions>
          <slot name="extra-actions"></slot>
        </template>
      </table-action>
      <slot name="table-action-append"></slot>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      size="mini"
      :module-path="modulePath"
      :default-query-params="queryParams"
      :visible-columns="visibleColumns"
      :row-class="getStudentFromClusterTableRowClass"
      @row-action="handleRowAction"
    >
      <template #custom-column-gotoAfterGraduation="{ record }">
        {{ record.gotoAfterGraduation?.target?.name }}
      </template>
    </crud-table>

    <student-archive
      v-if="currentStudent && archiveVisible"
      v-model:visible="archiveVisible"
      :base-info="currentStudent"
    />

    <update-status-modal
      v-if="currentStudent && updateStatusVisible"
      v-model:visible="updateStatusVisible"
      :records="currentStudent"
      @ok="handleRefresh"
    />

    <set-grade-class-modal
      v-if="currentStudent && setGradeClassVisible"
      v-model:visible="setGradeClassVisible"
      :records="currentStudent"
      @ok="handleRefresh"
    />

    <set-graduation-modal
      v-if="currentStudent && graduationVisible"
      v-model:visible="graduationVisible"
      :records="currentStudent"
      :rows="currentRowActionConfig.rows"
      @ok="handleRefresh"
    />

    <student-send-plan-modal v-model:visible="sendEducationPlanVisible" :student="currentStudent" />
    <committeeReferenceModal v-model:visible="specialCommitteeVisible" :student-ids="currentStudent" />
  </a-card>
</template>

<style scoped lang="scss">
  :deep {
    .clusterStudent-row .arco-table-td {
      background-color: rgba(var(--blue-2), 0.3);

      &:last-child,
      &:nth-child(1),
      &:nth-child(2) {
        background: #fff;
      }
    }
  }
</style>
