import { getClientRole, PROJECT_URLS } from '@repo/env-config';
import { CustomSchema } from '@repo/infrastructure/types';
import { DISORDER_TYPES, UNIT_NATURES_MAP } from '@repo/infrastructure/constants';
import { request } from '@repo/infrastructure/request';
import { defineAsyncComponent, Ref } from 'vue';
import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
import { isArray, omit } from 'lodash';
import { Message } from '@arco-design/web-vue';
import useCommonStore from '@repo/infrastructure/utils/store';
import { nationsList } from '@repo/infrastructure/data';
import { signatureInput } from '@repo/ui/components/form/inputComponents';
import { getAdminClientNature, getOrgNature } from '../src/utils/utils';
import { studentAttachmentsColumns } from '../src/constants';

const enrollmentYears: number[] = [];
const thisYear = new Date().getFullYear();
for (let i = 2018; i < thisYear + 2; i += 1) {
  enrollmentYears.push(i);
}

const rawPermissions = [
  'specialSchool:student:student:baseInfo:',
  'kindergarten:student:student:baseInfo:',
  'compulsoryEducation:student:student:baseInfo:',
  'vocationalEducation:student:student:baseInfo:',
  'student:',
];
const updatePermissions = rawPermissions.map((item) => `${item}edit`);

const studentSchema: CustomSchema = {
  api: '/resourceRoom/student',
  permissions: {
    create: rawPermissions.map((item) => `${item}create`),
    update: updatePermissions,
    delete: rawPermissions.map((item) => `${item}delete`),
    import: rawPermissions.map((item) => `${item}import`),
    export: rawPermissions.map((item) => `${item}export`),
  },
  importable: {
    enabled: true,
    template: 'https://tejiao-prod-static1.oss-cn-chengdu.aliyuncs.com/templates/v2学生导入模板.xlsx',
  },
  exportable: {
    enabled: false,
    columns: ['name', 'symbol', 'nation', 'gender', 'age', 'birthday', 'fusionSchool'],
  },
  listViewProps: {
    size: 'mini',
    rowActionWidth: 190,
    searchType: 'advance',
    filterFields: ['symbol', 'gender', 'name', 'disorders', 'rehabilitationInstitution', 'hasSendPlan'],
  },
  quickSearchProps: {
    enabled: true,
    fields: ['name', 'symbol', 'disorders', 'fusionSchoolName', 'gradeClass.className'],
    placeholder: '按姓名/学号/学校/残疾类型搜索',
  },
  rowActions: [
    // { key: 'updateGradeClass', label: '修改班级', handler() {}, multiple: true },
    // {
    //   key: 'updateGradeClass',
    //   label: '修改学校',
    //   permNode: updatePermissions,
    //   visible(records) {
    //     return true;
    //   },
    //   handler() {},
    //   multiple: true,
    // },
    // { key: 'updateGradeClass', label: '修改障碍类型', handler() {}, multiple: true },
    // { key: 'view', visible: false },
    {
      key: 'updateStatus',
      label: '修改状态',
      permNode: updatePermissions,
      multiple: true,
      visible(record) {
        return !record?.graduated;
      },
      handler() {},
    },
    {
      key: 'setGradeClass',
      label: '修改班级',
      multiple: true,
      permNode: updatePermissions,
      visible(record) {
        return !record?.graduated;
      },
      handler() {},
    },
    {
      key: 'sendEducationPlan',
      label: '送教计划',
      icon: 'icon-swap',
      permNode: 'teaching:teachingPlan:send',
      visible(record?: any) {
        return getClientRole() === 'Company' && record.hasSendPlan && !record?.graduated;
      },
      handler() {},
    },
    {
      key: 'archive',
      label: '数字档案',
      permNode: [
        'specialSchool:student:student:archive',
        'kindergarten:student:student:archive',
        'compulsoryEducation:student:student:archive',
        'vocationalEducation:student:student:archive',
        'student:archive',
      ],
      expose: true,
      handler() {},
    },
    {
      key: 'graduation',
      label: '毕业',
      icon: 'icon-send',
      multiple: true,
      visible(record) {
        return !record?.graduated;
      },
      permNode: updatePermissions,
      handler() {},
    },
    {
      key: 'specialCommittee',
      label: '专委会引入',
      handler: () => {},
      multiple: true,
    },
    {
      key: 'cancelGraduation',
      label: '取消毕业',
      icon: 'icon-close',
      visible(record) {
        return record?.graduated;
      },
      permNode: updatePermissions,
      handler() {},
    },
  ],
  formViewProps: {
    layout: 'horizontal',
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: [
          'name',
          // 'symbol',
          'nation',
          'gender',
          'birthday',
          'fusionSchool',
          'gradeClass',
          'enrollmentYear',
          'disabilityCertificateNo',
          'idCardNo',
          'typeOfEnrollment',
          'hasSendPlan',
        ],
        columns: 24,
        colSpan: 8,
      },
      {
        label: '康复信息',
        fields: [
          'disorders',
          'disabilityLevel',
          // 'rehabilitationInstitution',
          'rehabilitationInstitutionList',
          'homeDeliveryInstitution',
          'committee',
        ],
      },
      {
        label: '家庭状况',
        fields: ['guardian', 'guardianPhone', 'familyMembers'],
      },
      {
        label: '详细信息',
        fields: ['familyStatus', 'historyOfDevelopment', 'historyOfEducation'],
      },
      {
        fields: ['attachments'],
      },
    ],
  },
  detailViewProps: {
    columns: 4,
    type: 'modal',
    showOthers: false,
    component: defineAsyncComponent(() => import('@repo/components/student/customStudentDetail.vue')),
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: [
          'oneInchPhoto',
          'name',
          'symbol',
          'nation',
          'gender',
          'birthday',
          'fusionSchool',
          'gradeClass',
          'enrollmentYear',
          'disabilityCertificateNo',
          'idCardNo',
          'hasSendPlan',
        ],
      },
      {
        label: '康复信息',
        fields: ['disorders', 'disabilityLevel', 'rehabilitationInstitutionList', 'homeDeliveryInstitution'],
      },
      {
        label: '家庭状况',
        columns: 3,
        fields: ['guardian', 'guardianPhone', 'address', 'familyMembers'],
      },
      {
        label: '详细信息',
        columns: 1,
        fields: ['familyStatus', 'historyOfDevelopment', 'historyOfEducation'],
      },
      {
        columns: 4,
        fields: ['attachments'],
      },
    ],
  },
  fieldsMap: {
    name: {
      inputWidget: defineAsyncComponent(() => import('../src/student/components/studentNameInput.vue')),
      inputWidgetProps: {
        maxLength: 10,
      },
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
    },
    guid: {
      visibleInForm: false,
    },
    sourceStudentId: {
      visibleInTable: false,
      visibleInForm: false,
      visibleInDetail: false,
    },
    fusionSchool: {
      key: 'fusionSchool',
      label: '所属学校',
      required: true,
      defaultValue: () => {
        const clientRole = getClientRole();
        const school = useUserStore().getUserFusionSchool();
        return clientRole === 'Manager' ? null : omit(school, 'name');
      },
      inputWidget: 'selectInput',
      foreignField: {
        api: '/resourceCenter/fusionSchool',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        queryParams: async (record: any) => {
          const menuStore = useUserMenuStore();
          // const { branchOfficeId } = record.fusionSchool;
          const params = {};
          // params.branchOfficeId = branchOfficeId || useUserStore().getUserFusionSchool().branchOfficeId; // 管理端...可能有问题
          return {
            nature:
              menuStore.getCurrentMenuInfo().app?.label !== UNIT_NATURES_MAP.SpecialEduCommittee
                ? menuStore.getCurrentMenuInfo().app?.label
                : null, // to avoid the issue where the specialCommittee cannot load the correct school when edit student info
            ...params,
          };
        },
      },
      inputWidgetProps: {
        allowSearch: true,
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.id === value);
          if (!selected) {
            return undefined;
          }
          return {
            id: selected.raw?.id,
            name: selected.raw?.name,
            branchOfficeId: selected.raw?.branchOfficeId,
          };
        },
      },
    },
    familyMembers: {
      inputWidget: defineAsyncComponent(() => import('../src/student/studentFamilyInput.vue')),
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('../src/student/studentFamilyDisplay.vue')),
      },
    },
    gradeClass: {
      foreignField: {
        api: '/resourceRoom/gradeClass',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        queryParams: async () => {
          const school = useUserStore().getUserFusionSchool();
          return {
            boId: school.branchOfficeId,
          };
        },
      },
      inputWidgetProps: {
        labelField: 'name',
        valueField: 'id',
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.id === value);
          if (!selected) {
            return undefined;
          }
          return {
            id: selected.raw?.id,
            name: selected.raw?.name,
          };
        },
        queryDepends: {
          async onFusionSchoolChange(fusionSchool: any, inputValue: Ref<any>, formData: any, isInit: boolean) {
            if (!isInit) {
              inputValue.value = undefined;
            }
            if (fusionSchool?.branchOfficeId) {
              const { data } = await request('/resourceRoom/gradeClass', {
                params: {
                  boId: fusionSchool.branchOfficeId,
                  orgNature: getAdminClientNature(),
                },
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              return {
                inputWidgetProps: {
                  placeholder: data.items?.length ? '请选择班级' : '未找到年级班级信息',
                  disabled: false,
                  options: data.items || [],
                },
              };
            }
            return {
              inputWidgetProps: {
                placeholder: '',
                disabled: true,
                options: [],
              },
            };
          },
        },
      },
    },
    idCardNo: {
      inputWidgetProps: {
        maxLength: 18,
      },
    },
    guardianPhone: {
      displayProps: {
        detailSpan: 4,
      },
      inputWidgetProps: {
        maxLength: 11,
      },
    },
    familyStatus: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        colSpan: 24,
      },
    },
    // address: {
    //   inputWidgetProps: {
    //     colSpan: 12,
    //   },
    // },
    historyOfDevelopment: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        colSpan: 24,
      },
    },
    historyOfEducation: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        colSpan: 24,
      },
    },
    enrollmentYear: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        options: enrollmentYears,
      },
    },
    disabilityLevel: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['一级', '二级', '三级', '四级'],
      },
      filterable: true,
    },
    transferRemark: {
      visibleInForm: false,
      inputWidgetProps: {
        colSpan: 24,
      },
    },
    entranceType: {
      visibleInTable: false,
      visibleInForm: false,
      visibleInDetail: false,
      listProps: {
        columnWidth: 120,
      },
    },
    disorders: {
      dataType: 'Enum',
      valueType: 'Enum',
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: DISORDER_TYPES,
        allowCreate: true,
      },
      filterable: true,
    },
    gender: {
      dataType: 'Enum',
      valueType: 'Enum',
      listProps: {
        columnWidth: 70,
      },
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['男', '女'],
      },
      filterable: true,
      filterProps: {
        inputWidget: 'selectInput',
      },
    },
    symbol: {
      inputWidgetProps: {
        placeholder: '如不输入则由系统自动生成',
      },
    },
    age: {
      listProps: {
        columnWidth: 70,
      },
      displayProps: {
        toDisplay(val) {
          return val ? `${val} 岁` : '';
        },
      },
      filterable: true,
    },
    additionalData: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    nation: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: nationsList,
        allowSearch: true,
      },
      listProps: {
        columnWidth: 70,
      },
    },
    status: {
      listProps: {
        columnWidth: 70,
      },
      visibleInForm: false,
    },
    birthday: {
      listProps: {
        columnWidth: 130,
      },
      inputWidgetProps: {
        disabledDate: (currentDate: Date) => currentDate && currentDate.getTime() > Date.now(),
      },
      displayProps: {
        format: 'YYYY年MM月DD日',
        fullFormat: 'YYYY年MM月DD日',
      },
    },
    rehabilitationInstitution: {
      dataType: 'Foreign',
      valueType: 'Foreign',
      visibleInForm: false,
      foreignField: {
        api: '/resourceCenter/fusionSchool',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        queryParams: {
          institutionList: 1,
        },
      },
      displayProps: {
        toDisplay(val) {
          return val?.name || '';
        },
      },
      inputWidgetProps: {
        valueType: 'idName',
        allowClear: true,
        allowSearch: true,
      },
      filterProps: {
        targetField: 'rehabilitationInstitution',
      },
    },
    rehabilitationInstitutionList: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        colSpan: 12,
        multiple: true,
        getOptions: async () => {
          const store = useCommonStore({
            api: '/resourceCenter/fusionSchool',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            queryParams: {
              institutionList: 1,
            },
          });
          const raw = await store.getList();

          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        },
        valueType: (value: number[], computedOptions: any[]) => {
          const selected = computedOptions.filter((item) => {
            return value.includes(item.raw?.value);
          });
          if (!selected) {
            return undefined;
          }
          return selected.map((item) => {
            return {
              id: item.value,
              name: item.label,
              value: item.value,
              label: item.label,
            };
          });
        },
      },
      displayProps: {
        toDisplay: async (value, record) => {
          const store = useCommonStore({
            api: '/resourceCenter/fusionSchool',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            queryParams: {
              institutionList: 1,
            },
          });
          const raw = await store.getList();
          const nature = getOrgNature();
          const userStore = useUserStore();
          const userSchool = userStore.getUserFusionSchool();

          if (Array.isArray(value)) {
            const names = value.map((item) => {
              const found = raw.find((rehabilitationInstitution: any) => rehabilitationInstitution.id === item?.id);
              return found ? found.name : '';
            });
            // maybe has some problem
            if (nature !== '机构') return names.filter(Boolean).join('、') || '';
            return value.map((item) => item.name).join(' ,') || '';
          }
          return '';
        },
      },
    },
    homeDeliveryInstitution: {
      dataType: 'Foreign',
      valueType: 'Foreign',
      foreignField: {
        api: '/resourceCenter/fusionSchool',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        queryParams: {
          institutionList: 1,
        },
      },
      displayProps: {
        toDisplay(val) {
          return val?.name || '';
        },
      },
      inputWidgetProps: {
        valueType: 'idName',
        allowClear: true,
        allowSearch: true,
        disabled: true,
        queryDepends: {
          onHasSendPlanChange(val, raw, formData) {
            if (!val) {
              formData.value.homeDeliveryInstitution = undefined;
            }

            return {
              inputWidgetProps: {
                disabled: !val,
              },
            };
          },
        },
      },
      filterProps: {
        targetField: 'homeDeliveryInstitution',
      },
    },
    attachments: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: studentAttachmentsColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: studentAttachmentsColumns,
      },
    },
    typeOfEnrollment: {
      dataType: 'Enum',
      valueType: 'Enum',
      listProps: {
        columnWidth: 100,
      },
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['幼儿园', '小学', '初中', '高中', '职高'],
      },
      filterable: true,
      filterProps: {
        inputWidget: 'selectInput',
      },
    },
    hasSendPlan: {
      displayProps: {
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: () => {
            const permNode = [
              'specialSchool:student:student:isSend',
              'kindergarten:student:student:isSend',
              'compulsoryEducation:student:student:isSend',
              'vocationalEducation:student:student:isSend',
              'institution:student:isSend',
              'student:isSend',
            ];
            const userStore = useUserStore();
            return userStore.isAuthorized(permNode);
          },
          confirmMessage(value: boolean, record: any) {
            if (value) {
              return `确认将【${record.name}】改为送教学生？`;
            }
            return `确认将【${record.name}】改为非送教学生？`;
          },
          async handler(value: boolean, record: any) {
            try {
              await request(`/resourceRoom/student/${record.id}`, {
                method: 'PUT',
                data: {
                  ...record,
                  hasSendPlan: value,
                },
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              Message.success('操作成功');
              return value;
            } catch (error) {
              Message.error('操作失败');
              return !value;
            }
          },
        },
      },
    },

    intoSchoolAfterGraduation: {
      visibleInForm: false,
      displayProps: {
        toDisplay(value) {
          return value?.name;
        },
      },
    },
    physiologicalDiagnosticReport: {
      visibleInForm: false,
    },
    psychologyDiagnosticReport: {
      visibleInForm: false,
    },
    otherEducationalDiagnoses: {
      visibleInForm: false,
    },
    clusterIds: {
      visibleInForm: false,
    },
    specialEduCommitteeId: {
      visibleInForm: false,
    },
    isFromSubgroup: {
      visibleInForm: false,
    },
    committee: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        colSpan: 6,
        getOptions: async () => {
          const store = useCommonStore({
            api: '/org/branchOffice/specialCommittee',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            queryParams: {
              institutionList: 1,
            },
          });
          const raw = await store.getList();

          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
              raw: item,
            };
          });
        },
        valueType: (value: number, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.value === value);
          if (!selected) {
            return undefined;
          }
          return {
            id: selected.value,
            name: selected.label,
          };
        },
      },
    },
    //
  },
};

export default { studentSchema };
