import { PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';
import { CustomSchema } from '@repo/infrastructure/types';
import { useTeacherStore, useUserStore } from '@repo/infrastructure/store';
import { getAdminClientNature } from '../src/utils/utils';

const getDisplayName = (grade, className) => {
  const res = [];
  if (grade) {
    res.push(grade.name);
  }
  if (className) {
    res.push(className);
  }
  return res.join('');
};

const gradeClassSchema: CustomSchema = {
  api: '/resourceRoom/subgroup',
  modalEdit: true,
  listViewProps: {
    rowActionWidth: 260,
  },
  rowActions: [
    {
      key: 'teacherListMaintain',
      label: '负责人管理',
      handler() {},
      expose: true,
    },
    {
      key: 'studentListMaintain',
      label: '学生管理',
      handler() {},
      expose: true,
    },
  ],
  fieldsMap: {
    boId: {
      label: '所属单位',
      key: 'boId',
      required: true,
      inputWidget: 'selectInput',
      inputWidgetProps: {
        valueField: 'id',
        labelField: 'name',
        allowSearch: true,
        async getOptions() {
          const school = useUserStore().getUserFusionSchool();
          const { sysRoleSet } = useUserStore().userInfo;
          // 可以看见相同company 下的所有单位
          const resourceManagerName = ['资源中心管理员', '中心管理员', '超级管理员'];
          const { data: res } = await request(`/org/sysRole/getAllResourceManager`, {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            method: 'put',
            data: resourceManagerName,
          });
          const resourceManagerSysRoleId = res.items.map((item) => item.id);
          const params = sysRoleSet.includes(resourceManagerSysRoleId[0]) ? {} : { id: school.branchOfficeId };
          const { data } = await request('/org/branchOffice', {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            params: {
              name: '%_%',
              nature: getAdminClientNature(),
              ...params,
            },
          });
          return data.items || [];
        },
      },
      displayProps: {
        toDisplay: (val, record) => {
          return record?.branchOffice.name;
        },
      },
    },
    branchOffice: {
      visibleInForm: false,
      visibleInDetail: false,
    },
    managerUserIds: {
      label: '负责人',
      key: 'managerUserIds',
      inputWidget: 'selectInput',
      visibleInTable: false,
      visibleInForm: false,
      foreignField: {
        api: undefined,
      },
      inputWidgetProps: {
        labelField: 'name',
        valueField: 'id',
        allowSearch: true,
        valueType(id) {
          return {
            id,
          };
        },
        queryDepends: {
          async onBoIdChange(boId: any) {
            if (boId) {
              const teacherStore = useTeacherStore();
              const teachers = await teacherStore.getTeachersList();

              return {
                inputWidgetProps: {
                  placeholder: teachers.length ? '请选择班主任' : '当前学校无用户',
                  disabled: false,
                  options: teachers || [],
                },
              };
            }
            return {
              inputWidgetProps: {
                placeholder: '',
                disabled: true,
                options: [],
              },
            };
          },
        },
      },
      displayProps: {
        detailSpan: 2,
        toDisplay: (val, record) => {
          return (
            record.teacherList
              ?.filter((item) => val.includes(item.id))
              .map((teacher) => teacher.name)
              .join(' ,') || ''
          );
        },
      },
    },
    teacherList: {
      label: '教师',
      key: 'teacherList',
      visibleInForm: false,
      displayProps: {
        detailSpan: 2,
        toDisplay: (val, record) => {
          const { managerUserIds }: number[] = record;
          const manager = val
            .filter((item) => managerUserIds.includes(item.id))
            .map((item) => {
              return item.name;
            });
          const ordinaryTeacher = val
            .filter((item) => !managerUserIds.includes(item.id))
            .map((item) => {
              return `${item.name} `;
            });
          return ordinaryTeacher.length ? `${manager}（${ordinaryTeacher}）` : `${manager}`;
        },
      },
    },
    studentList: {
      visibleInForm: false,
      displayProps: {
        detailSpan: 1,
        toDisplay: (val: any, record) => {
          return val.map((item: any) => item.name).join(', ');
        },
      },
    },
    // managerUserIds: {
    //   label: '负责人',
    //   key: 'managerUserIds',
    //   visibleInForm: false,
    // },
    name: {
      label: '组名',
      key: 'name',
      inputWidget: 'textInput',
      required: true,
      inputWidgetProps: {
        queryDepends: {
          onGradeChange(grade, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.grade, formData.value.className);
          },
          onClassNameChange(grade, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.grade, formData.value.className);
          },
        },
      },
    },
    fusionSchool: {
      visibleInForm: false,
    },
    createdDate: {
      label: '创建于',
      key: 'createdDate',
      visibleInForm: false,
      displayProps: {
        toDisplay: (val, record) => {
          return val.split(' ')[0];
        },
      },
    },
    orgNature: {
      label: '单位性质',
      key: 'orgNature',
      defaultValue: getAdminClientNature,
      visibleInForm: false,
      visibleInTable: false,
    },
  },
};

export default { gradeClassSchema };
