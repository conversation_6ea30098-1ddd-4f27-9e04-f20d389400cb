import { Component, ref } from 'vue';
// @ts-ignore
import { cloneDeep, isArray } from 'lodash';
// @ts-ignore
import OrgSvg from '../ui/app-icon/org.svg';
// @ts-ignore
// import StudentSvg from '../ui/app-icon/student.svg';
// @ts-ignore
import InstitutionSvg from '../ui/app-icon/institution.svg';
// @ts-ignore
import ManageSvg from '../ui/app-icon/manage.svg';
// eslint-disable-next-line import/no-cycle
import useUserStore from '../store/user';
import { adminPermNodes, teacherPermNodes } from './permissions';
import { UNIT_NATURES } from '../constants';
import useMenuRenameStore from '../store/menuRenameStore';

export type UserMenuVisibleOptions = {
  user?: any;
};

export type UserMenu = {
  key: string;
  label: string;
  unitNature?: string;
  icon?: string | Component;
  permission?: string | string[] | boolean;
  children?: UserMenu[];
  visible?: boolean | ((options: UserMenuVisibleOptions) => boolean);
  isMenu?: boolean;
  link?: string | boolean;
  handleClick?: () => void;
  moduleRequired?: string;
  count?: number;
  dot?: boolean;
  component?: string;
  [props: string]: any;
};

const teachingTask = { key: 'mission', label: '教学任务', isMenu: false };

const userMenuVisible = (menu: UserMenu) => {
  if (menu.visible === false || menu.permission === false || menu.isMenu === false) {
    return false;
  }
  const userStore: any = useUserStore();
  if (userStore.branchOffice.school?.nature === '机构') {
    // 康复机构的教案应该单独存储
    teachingTask.key = 'design';
  }
  if (!menu.permission) {
    menu.permission = true;
  }
  if (menu.permission) {
    if (isArray(menu.permission) && !userStore.isAnyAuthorized(menu.permission)) {
      return false;
    }
    if (!userStore.isAuthorized(menu.permission)) {
      return false;
    }
  }
  if (menu.moduleRequired && !userStore.isModuleAuthorized(menu.moduleRequired)) {
    return false;
  }
  if (typeof menu.visible === 'function') {
    return menu.visible({ user: userStore.userInfo });
  }
  return true;
};

const teachingPlanMenus: UserMenu = {
  key: 'teachingPlan',
  label: '教育计划',
  isMenu: false,
  children: [
    {
      key: 'iep',
      label: '个别化教育计划',
      children: [
        { key: 'view', label: '查看' },
        { key: 'comment', label: '点评' },
      ],
    },
    { key: 'support', label: '支持计划' },
    { key: 'send', label: '送教计划' },
    { key: 'rehabilitation', label: '康复计划' },
  ],
};

const gradeManageMenu = { key: 'grade', label: '年级管理' };
const gradeClassManageMenu = { key: 'gradeClass', label: '班级管理' };
const groupingManageMenu = { key: 'subgroup', label: '分组管理' };

const studentMenus: UserMenu = {
  key: 'student',
  label: '学生管理',
  children: [
    {
      key: 'baseInfo',
      label: '基本信息',
      children: [
        { key: 'import', label: '导入', isMenu: false },
        { key: 'export', label: '导出', isMenu: false },
        { key: 'manage', label: '查看学生信息' },
        { key: 'create', label: '新增', isMenu: false },
        { key: 'edit', label: '编辑', isMenu: false },
        { key: 'delete', label: '删除', isMenu: false },
      ],
    },
    { key: 'archive', label: '数字档案', isMenu: false },
    { key: 'isSend', label: '是否送教', isMenu: false },
    {
      key: 'behavior',
      label: '行为记录',
      isMenu: false,
      children: [
        { key: 'view', label: '查看' },
        { key: 'comment', label: '点评' },
        { key: 'intervene', label: '行为干预记录' },
      ],
    },
    {
      key: 'assessment',
      isMenu: false,
      label: '评估记录',
      children: [
        { key: 'view', label: '查看' },
        { key: 'comment', label: '点评' },
      ],
    },
  ],
};

const graduatedStudentsMenu: UserMenu = {
  key: 'graduated',
  label: '毕业生信息库',
};

const teachingImplMenus: UserMenu = {
  key: 'teachingImpl',
  label: '教学实施',
  children: [
    { key: 'fusionClass', label: '融合课' },
    { key: 'resourceClass', label: '资源课' },
  ],
};

const adminMenus: UserMenu[] = [
  {
    key: 'adminAbility',
    label: '功能管理',
    isMenu: false,
    children: [
      {
        key: 'managerClient',
        label: '可使用管理端',
        desc: '是否可以使用管理端，如授权管理端权限请先选择此项',
      },
      {
        key: 'giantScreen',
        label: '可使用数据大屏',
      },
      {
        key: 'orgProfile',
        label: '可编辑本单位资料',
      },
      {
        key: 'mockUser',
        label: '可模拟本单位用户登陆',
        desc: '拥有此权限的用户将可以切换到其他帐号身份查看',
      },
    ],
  },
  {
    key: 'center',
    unitNature: UNIT_NATURES.Center,
    label: '资源中心',
    icon: OrgSvg,
    children: [
      {
        key: 'dailyWork',
        label: '日常业务',
        children: [
          { key: 'visit', label: '来访登记' },
          { key: 'supervision', label: '常规巡检' },
          /*          {
            key: 'supervision',
            label: '常规巡检',
            children: [
              { key: 'amendment', label: '指标修订' },
              { key: 'details', label: '评估详情' },
            ],
          }, */
          // {
          //   key: 'docTemplate',
          //   label: '文档模板',
          //   children: [
          //     { key: 'manage', label: '管理模板' },
          //     { key: 'download', label: '模板附件下载', isMenu: false },
          //   ],
          // },
          { key: 'event', label: '中心大事记' },
          {
            key: 'asset',
            label: '资产管理',
            children: [
              { key: 'assetInfo', label: '管理资产信息' },
              { key: 'inventory', label: '资产库存调整', isMenu: false },
              { key: 'application', label: '资产借用登记' },
              { key: 'applicationVerify', label: '申请审核/归还', isMenu: false },
              { key: 'transferLog', label: '资产库存变动情况记录' },
            ],
          },
          {
            key: 'paperCompetition',
            label: '论文赛课',
          },
        ],
      },
      {
        key: 'resource',
        label: '资源管理',
        children: [
          {
            key: 'drive',
            label: '云盘共享',
            children: [
              { key: 'use', label: '使用', isMenu: false },
              { key: 'manage', label: '管理', isMenu: false },
            ],
          },
          { key: 'category', label: '教康资源分类' },
          { key: 'course', label: '课程资源' },
          { key: 'teachingResource', label: '教学资源' },
          // { key: 'shared', label: '共享资源' },
        ],
      },
      {
        key: 'advisoryService',
        label: '咨询服务',
        children: [
          { key: 'verify', label: '申请审核' },
          {
            key: 'record',
            label: '巡回指导记录',
            children: [
              { key: 'manage', label: '管理' },
              { key: 'followUp', label: '回访记录' },
            ],
          },
          { key: 'conventionalAppointment', label: '常规指导预约' },
          { key: 'conventionalTeacher', label: '常规指导教师' },
        ],
      },
      {
        key: 'conference',
        label: '会议培训',
        children: [
          { key: 'conference', label: '会议培训发布' },
          { key: 'record', label: '会议培训记录' },
          {
            key: 'certificate',
            label: '会议培训证明',
            children: [
              { key: 'manage', label: '证明管理' },
              { key: 'template', label: '打印模板设计', isMenu: false },
            ],
          },
          { key: 'statistics', label: '会议培训统计' },
          { key: 'assessmentStatistics', label: '融合教育考核统计' },
        ],
      },
      {
        key: 'weapp',
        label: '小程序管理',
        children: [
          { key: 'visitReservation', label: '到校预约' },
          { key: 'conversation', label: '咨询记录' },
          // { key: 'guardianCourse', label: '家长课程管理' },
          { key: 'announcement', label: '公告管理' },
          { key: 'news', label: '新闻管理' },
          { key: 'newsCategory', label: '新闻分类', isMenu: false },
          { key: 'carousel', label: '轮播图管理' },
        ],
      },
      {
        key: 'questionnaire',
        label: '问卷表单',
      },
      /* {
        key: 'test',
        label: '测试中心',
      }, */
      {
        key: 'attentionTrainingManage',
        label: '注意力训练管理',
        children: [{ key: 'experimentalParadigmManagement', label: '实验范式管理' }],
      },
      {
        key: 'dataCenter',
        label: '数据中心',
        children: [{ key: 'sendStatistics', label: '送教统计' }],
      },
    ],
  },
  {
    key: 'specialSchool',
    unitNature: UNIT_NATURES.Special,
    label: '特殊教育学校', // '特殊教育学校',
    iconText: '特',
    children: [
      {
        key: 'student',
        label: '学生管理',
        children: [
          { ...studentMenus },
          { ...gradeManageMenu },
          { ...gradeClassManageMenu },
          { ...groupingManageMenu },
          { ...graduatedStudentsMenu },
        ],
      },
      {
        key: 'assessment',
        label: '评估管理',
        children: [
          {
            key: 'criterion',
            label: '评估管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          {
            key: 'criterionCategory',
            label: '评估分类管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          { key: 'questionLibrary', label: '综合试题库管理' },
          {
            key: 'supervision',
            label: '常规巡检',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'teacher',
        label: '教师管理',
        children: [
          {
            key: 'teacherInfo',
            label: '教师信息管理',
            children: [
              { key: 'manage', label: '教师基本信息管理' },
              { key: 'import', label: '导入', isMenu: false },
              { key: 'export', label: '导出', isMenu: false },
              { key: 'verify', label: '注册审核', isMenu: false },
            ],
          },
          { key: 'supervision', label: '教学数据监测' },
          {
            key: 'courseLibrary',
            label: '教案库',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'manage', label: '管理', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'teachingSubject',
        label: '教学科目',
        children: [
          { key: 'view', label: '查看', isMenu: false },
          { key: 'create', label: '新增', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
        ],
      },
      // { key: 'classSchedule', label: '班级排课' },
      { key: 'scheduleTool', label: '班级排课' },
      { key: 'timetable', label: '课表管理' },
    ],
  },
  {
    key: 'kindergarten',
    unitNature: UNIT_NATURES.Kindergarten,
    label: '幼儿园', // '幼儿园',
    iconText: '幼',
    children: [
      {
        key: 'student',
        label: '学生管理',
        children: [
          { ...studentMenus },
          { ...graduatedStudentsMenu },
          { ...groupingManageMenu },
          // , { ...gradeManageMenu }, { ...gradeClassManageMenu }
        ],
      },
      {
        key: 'teacher',
        label: '教师管理',
        children: [
          { key: 'teacherInfo', label: '教师信息管理' },
          // { key: 'supervision', label: '教学督导' },
          { key: 'supervision', label: '教学数据监测' },
          {
            key: 'courseLibrary',
            label: '教案库',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'manage', label: '管理', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'assessment',
        label: '评估管理',
        children: [
          {
            key: 'criterion',
            label: '评估管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          {
            key: 'criterionCategory',
            label: '评估分类管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          { key: 'questionLibrary', label: '综合试题库管理' },
          {
            key: 'supervision',
            label: '常规巡检',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'teachingSubject',
        label: '教学科目',
        children: [
          { key: 'view', label: '查看', isMenu: false },
          { key: 'create', label: '新增', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
        ],
      },
    ],
  },
  {
    key: 'compulsoryEducation',
    unitNature: UNIT_NATURES.Normal,
    label: '普通教育学校', // '普通教育学校',
    iconText: '普',
    children: [
      {
        key: 'student',
        label: '学生管理',
        children: [
          { ...studentMenus },
          { ...graduatedStudentsMenu },
          { ...groupingManageMenu },
          // , { ...gradeManageMenu }, { ...gradeClassManageMenu }
        ],
      },
      {
        key: 'teacher',
        label: '教师管理',
        children: [
          { key: 'teacherInfo', label: '教师信息管理' },
          // { key: 'supervision', label: '教学督导' },
          { key: 'supervision', label: '教学数据监测' },

          {
            key: 'courseLibrary',
            label: '教案库',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'manage', label: '管理', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'assessment',
        label: '评估管理',
        children: [
          {
            key: 'criterion',
            label: '评估管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          {
            key: 'criterionCategory',
            label: '评估分类管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          { key: 'questionLibrary', label: '综合试题库管理' },
          {
            key: 'supervision',
            label: '常规巡检',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'teachingSubject',
        label: '教学科目',
        children: [
          { key: 'view', label: '查看', isMenu: false },
          { key: 'create', label: '新增', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
        ],
      },
    ],
  },
  {
    key: 'vocationalEducation',
    unitNature: UNIT_NATURES.Vocational,
    label: '职业教育学校', // '职业教育学校',
    iconText: '职',
    children: [
      {
        key: 'student',
        label: '学生管理',
        children: [
          { ...studentMenus },
          { ...graduatedStudentsMenu },
          { ...groupingManageMenu },
          // , { ...gradeManageMenu }, { ...gradeClassManageMenu }
        ],
      },
      {
        key: 'teacher',
        label: '教师管理',
        children: [
          { key: 'teacherInfo', label: '教师信息管理' },
          // { key: 'supervision', label: '教学督导' },
          { key: 'supervision', label: '教学数据监测' },

          {
            key: 'courseLibrary',
            label: '教案库',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'manage', label: '管理', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'assessment',
        label: '评估管理',
        children: [
          {
            key: 'criterion',
            label: '评估管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          {
            key: 'criterionCategory',
            label: '评估分类管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          { key: 'questionLibrary', label: '综合试题库管理' },
          {
            key: 'supervision',
            label: '常规巡检',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'teachingSubject',
        label: '教学科目',
        children: [
          { key: 'view', label: '查看', isMenu: false },
          { key: 'create', label: '新增', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
        ],
      },
      {
        key: 'supportingEmploymentServices',
        label: '支持性就业服务',
        children: [
          { key: 'employmentAssessment', label: '就业评估管理' },
          { key: 'employmentCounseling', label: '就业辅导管理' },
        ],
      },
    ],
  },
  {
    key: 'institution',
    unitNature: UNIT_NATURES.Institution,
    label: '机构', // '机构',
    children: [
      // { key: 'manage', label: '机构信息管理' },
      {
        key: 'teacher',
        label: '人员管理',
        children: [
          { key: 'staff', label: '人员管理' },
          // { key: 'supervision', label: '教学督导' },
          { key: 'supervision', label: '教学数据监测' },
          {
            key: 'courseLibrary',
            label: '教案库',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'manage', label: '管理', isMenu: false },
            ],
          },
        ],
      },

      /* {
        key: 'plan',
        label: '康复计划',
        children: [
          { key: 'manage', label: '管理', isMenu: false },
          { key: 'comment', label: '评测', isMenu: false },
        ],
      }, */
      {
        key: 'teachingSubject',
        label: '教学科目',
        children: [
          { key: 'view', label: '查看', isMenu: false },
          { key: 'create', label: '新增', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
        ],
      },
      // { key: 'design', label: '康复设计' },
      { key: 'student', label: '学生管理', children: [{ ...studentMenus }, { ...groupingManageMenu }] },
      {
        key: 'assessment',
        label: '评估管理',
        children: [
          {
            key: 'criterion',
            label: '评估管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          {
            key: 'criterionCategory',
            label: '评估分类管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          { key: 'rehabilitation', label: '康复评估' },
          {
            key: 'supervision',
            label: '常规巡检',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
        ],
      },
    ],
    icon: InstitutionSvg,
  },
  {
    key: 'specialEduCommittee',
    unitNature: UNIT_NATURES.SpecialEducationCommittee,
    label: '特教专委会', // '特教专委会',
    iconText: '专',
    children: [
      {
        key: 'student',
        label: '学生管理',
        children: [
          { key: 'resettled', label: '已安置' },
          { key: 'not-resettled', label: '待安置' },
          { key: 'resettle', label: '设置学生安置', isMenu: false },
        ],
      },
      {
        key: 'assessment',
        label: '评估管理',
        children: [
          {
            key: 'criterion',
            label: '评估管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
          {
            key: 'criterionCategory',
            label: '评估分类管理',
            children: [
              { key: 'view', label: '查看', isMenu: false },
              { key: 'create', label: '新增', isMenu: false },
              { key: 'edit', label: '编辑', isMenu: false },
              { key: 'delete', label: '删除', isMenu: false },
            ],
          },
        ],
      },
      {
        key: 'teacher',
        label: '人员管理',
        children: [
          { key: 'teacher', label: '人员管理' },
          // { key: 'placementSupervision', label: '安置督导' },
        ],
      },
      {
        key: 'dataMonitoring',
        label: '数据监测',
        children: [
          { key: 'placementAssessment', label: '安置评估' },
          { key: 'CBCLAssessment', label: 'CBCL评估' },
          { key: 'placementReport', label: '安置报告' },
        ],
      },
    ],
    icon: InstitutionSvg,
  },
  {
    key: 'system',
    label: '系统设置',
    icon: ManageSvg,
    children: [
      {
        key: 'org',
        label: '组织管理',
        children: [
          {
            key: 'user',
            label: '用户管理',
            children: [
              { key: 'manage', label: '用户信息管理' },
              { key: 'import', label: '导入用户', isMenu: false },
              { key: 'verify', label: '用户审核', isMenu: false },
            ],
          },
          { key: 'org', label: '单位管理', children: [{ key: 'import', label: '单位导入', isMenu: false }] },
          { key: 'authorize', label: '授权管理' },
          { key: 'titles', label: '用户职位管理' },
        ],
      },
      {
        key: 'utils',
        label: '系统工具',
        children: [{ key: 'poster', label: '海报管理' }],
      },
      {
        key: 'configuration',
        label: '中心配置',
        children: [
          // { key: 'sendConfiguration', label: '送教配置' },
          { key: 'system', label: '系统设置' },
          { key: 'todoConfiguration', label: '待办事项配置' },
          { key: 'customizedComponents', label: '自定义组件' },
          { key: 'customizedMenusName', label: '自定菜单名' },
        ],
      },
    ],
  },
];

const adminMenusMap: Record<string, UserMenu> = {};
const teacherMenus: UserMenu[] = [
  {
    key: 'teacherAbility',
    label: '功能管理',
    isMenu: false,
    children: [
      {
        key: 'teacherClient',
        label: '可使用教师端',
        desc: '是否可以使用教师端，如授权教师端权限请先选择此项',
      },
    ],
  },
  {
    key: 'student',
    label: '我的学生',
    icon: 'icon-xuesheng',
    // icon: StudentSvg,
    children: [
      { key: 'transfer', label: '转学管理', isMenu: false },
      { key: 'view', label: '查看', isMenu: false },
      { key: 'create', label: '新增', isMenu: false },
      { key: 'edit', label: '编辑', isMenu: false },
      { key: 'delete', label: '删除', isMenu: false },
      { key: 'archive', label: '数字档案', isMenu: false },
      { key: 'isSend', label: '是否送教', isMenu: false },
    ],
  },
  /* {
        key: 'behavior', // behavior
        label: '行为管理',
        icon: 'icon-xuesheng',
        // icon: StudentSvg,
        children: [
          { key: 'view', label: '查看', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
          { key: 'addEvent', label: '添加事件', isMenu: false },
          { key: 'startDiagnosis', label: '开始诊断', isMenu: false },
          { key: 'continueDiagnosis', label: '继续诊断', isMenu: false },
          { key: 'executePolicy', label: '策略执行', isMenu: false },
        ],
      }, */
  {
    key: 'teaching',
    label: '教康管理',
    icon: 'icon-jiaoxue',
    children: [
      { key: 'screeningEvaluation', label: '筛查评估' },
      { key: 'assessment', label: '评估管理' },
      { key: 'recognizeCharacters', label: '识字词' },
      { key: 'childBehavior', label: '儿心评估' },
      {
        key: 'teachingPlan',
        label: '干预计划',
        children: [
          {
            key: 'iepPlan',
            label: '个别化教育计划',
            children: [{ key: 'archive', label: '归档', isMenu: false }],
          },
          {
            key: 'supportPlan',
            label: '支持计划',
            children: [
              { key: 'assessment', label: '评估', isMenu: false },
              { key: 'toIep', label: '转个别化', isMenu: false },
            ],
          },
          {
            key: 'send',
            label: '送教计划',
            children: [
              { key: 'archive', label: '归档', isMenu: false },
              { key: 'export', label: '导出', isMenu: false },
            ],
          },
          { key: 'rehabilitation', label: '康复计划' },
        ],
      },
      {
        key: 'teachingImpl',
        label: '教康实施',
        children: [
          {
            key: 'course',
            label: '我教的课',
            children: [
              { key: 'archive', label: '教学档案', isMenu: false },
              { ...teachingTask }, // { key: 'mission', label: '教学任务', isMenu: false }, // 这里应该修改为design 如果是机构教师用户的话
              { key: 'behavior', label: '行为记录', isMenu: false },
              { key: 'assessment', label: '课后评测', isMenu: false },
              { key: 'question', label: '综合试题', isMenu: false },
              { key: 'refer', label: '从教案库引用', isMenu: false },
            ],
          },
          {
            key: 'rehabilitation',
            label: '康复设计',
            // children: [
            // { key: 'archive', label: '康复档案', isMenu: false },
            // { key: 'mission', label: '康复任务', isMenu: false },
            // { key: 'questionLib', label: '题库', isMenu: false },
            // ],
          },
          { key: 'hsc', label: '家校联系' },
          // { key: 'resourceClass', label: '资源课' },
          // { key: 'fusionClass', label: '融合课' },
          // { key: 'behavior', label: '行为记录分析' },
        ],
      },
    ],
  },
  {
    key: 'resource',
    label: '资源支持',
    icon: 'icon-ziyuan',
    children: [
      {
        key: 'drive',
        label: '云盘共享',
        children: [
          { key: 'use', label: '使用', isMenu: false },
          { key: 'manage', label: '管理', isMenu: false },
        ],
      },
      { key: 'classResource', label: '课程资源' },
      // { key: 'shared', label: '共享资源' },
    ],
  },
  {
    key: 'dailyWork',
    label: '日常事务',
    icon: 'icon-daily-affairs',
    children: [
      { key: 'workPlan', label: '工作计划' },
      { key: 'resourceRoomEvent', label: '资源教室大事记' },
      {
        key: 'advisoryService',
        label: '咨询服务',
        children: [
          { key: 'd2dService', label: '上门服务申请', isMenu: false },
          { key: 'tourGuide', label: '巡回指导申请', isMenu: false },
        ],
      },
      {
        key: 'normalTourGuide',
        label: '常巡教师预约',
        children: [
          { key: 'create', label: '新增', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
          { key: 'view', label: '查看', isMenu: false },
        ],
      },
      { key: 'assetBorrow', label: '资产借用' },
      // { key: 'supervision', label: '督导评分' },
    ],
  },
  {
    key: 'research',
    label: '科研课题',
    icon: 'icon-keyan',
    children: [
      { key: 'project', label: '课题研究' },
      { key: 'awards', label: '各类获奖' },
    ],
  },
  // {
  //   key: 'rehabilitation',
  //   label: '康复管理',
  //   icon: 'icon-kfgl',
  //   children: [
  //     { key: 'assessment', label: '康复评估' },
  //     { key: 'plan', label: '康复计划' },
  //     { key: 'design', label: '康复设计' },
  //   ],
  // },
  {
    key: 'training',
    label: '康复训练',
    icon: 'icon-daily-affairs',
    children: [
      {
        key: 'behavior', // behavior
        label: '行为分析',
        children: [
          /*          { key: 'view', label: '查看', isMenu: false },
                              { key: 'edit', label: '编辑', isMenu: false }, */
          { key: 'delete', label: '删除', isMenu: false },
          { key: 'addEvent', label: '添加事件', isMenu: false },
          { key: 'startDiagnosis', label: '开始诊断', isMenu: false },
          { key: 'continueDiagnosis', label: '继续诊断', isMenu: false },
          { key: 'executePolicy', label: '策略执行', isMenu: false },
        ],
      },
      {
        key: 'attentionTraining',
        label: '注意力训练',
        children: [
          { key: 'view', label: '查看', isMenu: false },
          { key: 'edit', label: '编辑', isMenu: false },
          { key: 'delete', label: '删除', isMenu: false },
        ],
      },
    ],
  },
  {
    key: 'employmentCounseling',
    label: '就业辅导',
    icon: 'icon-kfgl',
    children: [{ key: 'employmentService', label: '就业服务' }],
  },
  {
    key: 'specialEduCommittee',
    label: '专委会',
    children: [
      { key: 'placementAssessment', label: 'CBCL评估' },
      { key: 'placementReport', label: '安置报告' },
    ],
  },
];
const teacherMenusMap: Record<string, UserMenu> = {};

const getAllNodes = (type: 'admin' | 'teacher') => {
  const raw = cloneDeep(type === 'admin' ? adminMenus : teacherMenus);
  // 递归为菜单添加 permission，如果包含children 则permission为 module:
  const recursivePermission = (menus: UserMenu[], parentKey = '') => {
    const result: any[] = [];
    menus.forEach((menu) => {
      menu = cloneDeep(menu);
      menu.catalog = type;
      const key = parentKey ? `${parentKey}:${menu.key}` : menu.key;
      if (menu.children) {
        menu.children = recursivePermission(menu.children, key);
        menu.permission = `${key}:*`;
      } else {
        menu.permission = key;
        if (menu.isMenu) {
          menu.link = `/${key.replace(/:/g, '/')}`;
        }
      }
      result.push(menu);
    });
    return result;
  };

  return recursivePermission([...raw]);
};

const adminAllMenus = getAllNodes('admin');
const teacherAllMenus = getAllNodes('teacher');

const getUserMenus = (type: 'admin' | 'teacher') => {
  const menuRenameStore = useMenuRenameStore();
  const raw = cloneDeep(type === 'admin' ? adminAllMenus : teacherAllMenus);
  const linkPrefix = type === 'admin' ? 'manage' : 'teacher';
  // 递归过滤菜单，如果 userMenuVisible 返回 false 则删除，如果 没有link 且没有children 则删除
  const recursiveFilter = (menus: UserMenu[], parentKey = '') => {
    for (let i = menus.length - 1; i >= 0; i -= 1) {
      const menu = menus[i];
      const key = parentKey ? `${parentKey}:${menu.key}` : menu.key;
      if (!userMenuVisible(menu)) {
        menus.splice(i, 1);
      } else if (menu.link === undefined) {
        menu.link = `/${linkPrefix}/${key.replace(/:/g, '/')}`;
      }

      if (menu.children) {
        recursiveFilter(menu.children, key);
      }

      // if (menu.children && menu.children.length === 1 && parentKey) {
      //   menu.children = [];
      // }
    }
    return menus;
  };
  const filteredMenus = recursiveFilter(raw);
  return menuRenameStore.getRenamedMenus(type, filteredMenus);
};

const permissionToLabel = (permission?: string) => {
  if (!permission) {
    return '无此模块访问权限';
  }
  // admin_org:companyUser:manage => 组织管理-教师管理-教师信息管理
  const permissionNodes = permission.split(':');
  let allNodes = permissionNodes[0]?.indexOf('admin') === 0 ? adminPermNodes : teacherPermNodes;
  permissionNodes.shift();
  const labels: string[] = [];
  for (let i = 0; i < permissionNodes.length; i += 1) {
    const node = allNodes.find((n) => n.resource === permissionNodes[i]);
    if (!node) {
      labels.push(permissionNodes[i]);
      break;
    }
    labels.push(node.label);
    allNodes = node.children || [];
  }

  if (labels.length === 0) {
    return '无此模块访问权限';
  }

  return labels.join('-');
};

export {
  userMenuVisible,
  adminAllMenus,
  adminMenusMap,
  teacherAllMenus,
  teacherMenusMap,
  getUserMenus,
  permissionToLabel,
};
