<template>
  <a-form
    v-if="schemaModel && ready"
    ref="formRef"
    :model="formData"
    v-bind="{ ...(schemaModel.formViewProps || {}), ...$attrs }"
    auto-label-width
    :layout="schemaModel.formViewProps.layout || (fieldsGroups?.length > 1 ? 'vertical' : 'horizontal')"
    @submit="handleSubmit"
  >
    <slot name="prepend" :schema="schemaModel" :form-data="formData" />
    <a-card
      v-for="(fg, gi) in fieldsGroups.filter((fg: any) => fg.fields?.length > 0)"
      :key="gi"
      size="small"
      :bordered="cardBordered"
      class="mb-2"
      :title="fg.label === '__DEFAULT__' ? '' : translate(fg.label)"
    >
      <a-row :gutter="16">
        <a-col v-for="(f, fi) in fg.fields" :key="fi" :span="f.inputWidgetProps?.colSpan || fg.colSpan || 24">
          <form-item
            :ref="`formItem_${f.key}`"
            v-model="formData[f.key as string]"
            v-model:record="formData"
            :field-key="f.key!"
            :schema="schemaModel"
            @update:model-value="(val) => handleValueUpdate(f, val)"
          >
            <slot :name="`custom-input-${f.key}`" :field="f" :raw="formData"></slot>
          </form-item>
        </a-col>
      </a-row>
    </a-card>
    <slot :schema="schemaModel" :form-data="formData"></slot>
    <a-card v-if="showActions" class="mt" :bordered="cardBordered">
      <a-form-item>
        <slot name="form-actions" :schema="schemaModel" :form-data="formData">
          <a-space direction="horizontal">
            <a-button html-type="submit" :loading="loading" type="primary">{{ submitText || '保存' }}</a-button>
            <a-popconfirm content="确认要取消吗？" @ok="handleCancel">
              <a-button v-if="showCancel">{{ cancelText || '取消' }}</a-button>
            </a-popconfirm>
          </a-space>
        </slot>
      </a-form-item>
    </a-card>
  </a-form>
  <a-skeleton v-else animation>
    <a-skeleton-line :rows="5" />
  </a-skeleton>
</template>

<script lang="ts" setup>
  import { Schema, SchemaField } from '@repo/infrastructure/types';
  import {
    computed,
    getCurrentInstance,
    inject,
    nextTick,
    onMounted,
    PropType,
    provide,
    Ref,
    ref,
    toRaw,
    watch,
  } from 'vue';
  import { translate } from '@repo/infrastructure/utils';
  import { Message } from '@arco-design/web-vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CommonApi } from '@repo/infrastructure/crud';
  import { useLoading } from '@repo/infrastructure/hooks';
  import FormItem from './inputComponents/formItem.vue';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    schema: {
      type: Object as () => Schema,
      required: true,
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    submitText: {
      type: String,
      default: null,
    },
    cancelText: {
      type: String,
      default: null,
    },
    callback: {
      type: Function,
      required: false,
    },
    visibleColumns: {
      type: Array as PropType<string[]>,
    },
  });

  const app: any = getCurrentInstance();

  const emits = defineEmits(['update:modelValue']);
  const formRef = ref<any>(null);
  const ready = ref(false);
  const router: any = inject('router');
  const route = router.currentRoute.value || router.currentRoute;

  const recordId = computed<any>(() => {
    if (!route) {
      return 0;
    }
    return parseInt((route.params?.id as string) || (route.query?.id as string) || props.modelValue?.id, 10);
  });
  const isEditing = computed(() => recordId.value > 0);

  const schemaModel: Schema =
    props.schema.schemaFields && props.schema.schemaFields.length
      ? props.schema
      : SchemaHelper.getInstanceByApi(props.schema.api || '');
  let commonApi: any;

  const hasQueryDependsFields = ref<string[]>([]);

  const { loading, setLoading } = useLoading();

  const formData = computed({
    get() {
      return props.modelValue;
    },
    set(val: Record<string, any>) {
      emits('update:modelValue', val);
    },
  });
  const handleCancel = () => {
    router.back();
  };

  const handleSubmit = async (raw: any, values: any) => {
    setLoading(true);
    if (raw && raw.errors) {
      const errorFields = Array.from(Object.values(raw.errors), (item: any) => item.label).join('、');
      const validationErrorMsg = `请检查 ${errorFields} 等是否已正确填写.`;
      Message.error(validationErrorMsg);
      setLoading(false);
      throw new Error(validationErrorMsg);
    }

    const data = {};
    Object.keys(formData.value).forEach((key) => {
      if (typeof props.schema?.fieldsMap[key]?.inputWidgetProps?.formatValue === 'function') {
        data[key] = props.schema?.fieldsMap[key]?.inputWidgetProps?.formatValue(formData.value[key]);
      } else {
        data[key] = formData.value[key];
      }
    });

    try {
      let res;
      if (isEditing.value) {
        res = await commonApi.updateRecord(data, {
          baseURL: props.schema.baseURL,
        });
      } else {
        res = await commonApi.createRecord(data, {
          baseURL: props.schema.baseURL,
        });
      }

      Message.success('保存成功');
      if (typeof props.callback === 'function') {
        props.callback(res.data);
      } else {
        router.back();
      }
    } finally {
      setLoading(false);
    }
  };

  // grouping via schemaModel.fieldsGrouping
  const fieldsGroups = ref<any>(
    SchemaHelper.getFieldsGrouping(
      schemaModel,
      schemaModel.formViewProps?.fieldsGrouping || [],
      (field: SchemaField) => {
        if (props.visibleColumns?.length && !props.visibleColumns.includes(field.key)) {
          return true;
        }
        return (
          field.editable === false ||
          field.systemGenerated ||
          field.visibleInForm === false ||
          (field.updatable === false && isEditing.value)
        );
      },
    ),
  );

  const cardBordered = computed(() => {
    return fieldsGroups.value.length > 1;
  });

  const handleRecordChange = async (fieldKey: string, value: any, isInit?: boolean) => {
    hasQueryDependsFields.value.forEach((key) => {
      const formItemRef = app.refs[`formItem_${key}`]?.length ? app.refs[`formItem_${key}`][0] : undefined;
      if (formItemRef) {
        formItemRef.handleRecordChange(fieldKey, value, isInit);
      }
    });
  };

  const handleValueUpdate = (field: SchemaField, value: any) => {
    if (typeof field.inputWidgetProps?.onValueChange === 'function') {
      field.inputWidgetProps.onValueChange(value, formData);
    }

    handleRecordChange(field.key, value);
  };

  provide('formData', formData);

  defineExpose({
    formRef,
    formData,
    handleSubmit,
    handleCancel,
    handleValueUpdate,
    handleRecordChange,
  });

  onMounted(async () => {
    commonApi = CommonApi.getInstance(schemaModel);
    if (!isEditing.value) {
      // eslint-disable-next-line guard-for-in,no-restricted-syntax
      for (const index in schemaModel.schemaFields) {
        const f = schemaModel.schemaFields[index];

        if (f.inputWidgetProps?.queryDepends) {
          hasQueryDependsFields.value.push(f.key);
        }

        if (
          schemaModel.formViewProps?.defaultData !== undefined &&
          schemaModel.formViewProps?.defaultData[f.key] !== undefined
        ) {
          formData.value[f.key] = schemaModel.formViewProps?.defaultData[f.key];
        }

        if (formData.value[f.key] === undefined) {
          if (!f || !f.key || f.defaultValue === undefined || f.editable === false) {
            // eslint-disable-next-line no-continue
            continue;
          }
          if (typeof f.defaultValue === 'function') {
            // eslint-disable-next-line no-await-in-loop
            formData.value[f.key] = f.defaultValue(props.modelValue);
          } else {
            formData.value[f.key] = f.defaultValue;
          }
        }
      }
      ready.value = true;
    } else {
      schemaModel.schemaFields.forEach((f) => {
        if (f.inputWidgetProps?.queryDepends) {
          hasQueryDependsFields.value.push(f.key);
        }
      });

      const { data } = await commonApi.fetchOne(recordId.value, {}, { baseURL: schemaModel.baseURL });
      let editDataRaw = data;
      if (typeof props.schema?.formViewProps?.formatData === 'function') {
        editDataRaw = await props.schema.formViewProps.formatData(data);
      }

      formData.value = editDataRaw;

      ready.value = true;

      await nextTick(() => {
        Object.keys(formData.value).forEach((key) => {
          handleRecordChange(key, formData.value[key], true);
        });
      });
    }
  });
</script>

<script lang="ts">
  export default {
    name: 'CrudForm',
  };
</script>
