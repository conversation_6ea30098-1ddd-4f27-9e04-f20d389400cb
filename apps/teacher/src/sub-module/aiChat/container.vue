<script setup lang="ts">
  import AiDialogBox from '@repo/components/common/aiDialogBox.vue';
  import { ref, onMounted, nextTick } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useRoute } from 'vue-router';

  const chatModalVisible = ref(true);
  const aiDialogRef = ref(null);
  const chatResponse = ref('');
  const sessionId = ref('');
  const urlParams = new URLSearchParams(window.location.search);

  const route = useRoute();
  const token = ref(null);

  const requestStreamChat = async (msg: string) => {
    const requestUrl = new URL(`${PROJECT_URLS.MAIN_PROJECT_API}/resourceRoom/behaviorAnalysis/chat/streamCall`);
    requestUrl.searchParams.append('message', msg);
    requestUrl.searchParams.append('sessionId', sessionId.value);

    const response = await fetch(requestUrl.toString(), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Authorization': `Bearer ${token.value}`,
        'LoginSource': 'PC',
        'Accept-Language': 'zh-CN',
        'X-User-Type': 'System',
        'Request-Client-Role': 'Company',
      },
    });

    const reader = response.body?.getReader();
    const decoder = new TextDecoder('utf-8');

    let partial = '';
    while (true) {
      // eslint-disable-next-line no-await-in-loop
      const { value, done } = await reader.read();
      if (done) {
        aiDialogRef.value?.saveChatHistory();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      partial += chunk;

      const lines = partial.split('\n');

      partial = lines.pop() || '';

      // eslint-disable-next-line no-restricted-syntax
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const jsonText = line.slice(5).trim();
          try {
            const json = JSON.parse(jsonText);
            const text = json.output?.text || '';
            sessionId.value = json.output?.sessionId || '';
            chatResponse.value += text;
          } catch (err) {
            console.error('⚠️ JSON 解析失败：', jsonText);
          }
        }
      }
    }
  };

  const handleChat = async (msg: string) => {
    await requestStreamChat(msg);
  };
  onMounted(async () => {
    await nextTick();
    const url = window.location.href;
    const urlParam = new URLSearchParams(new URL(url).search);
    token.value = urlParam.get('token');
  });
</script>

<template>
  <aiDialogBox ref="aiDialogRef" v-model:visible="chatModalVisible" :response="chatResponse" @send="handleChat" />
</template>

<style scoped lang="scss">
  @tailwind base;
  @tailwind components;
  @tailwind utilities;

  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
</style>
